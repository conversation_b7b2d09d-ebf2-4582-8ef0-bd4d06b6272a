{"repository": {"url": "https://github.com/remotion-dev/remotion/tree/main/packages/studio"}, "name": "@remotion/studio", "version": "4.0.314", "description": "APIs for interacting with the Remotion Studio", "main": "dist", "sideEffects": false, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "bugs": {"url": "https://github.com/remotion-dev/remotion/issues"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"semver": "7.5.3", "memfs": "3.4.3", "source-map": "0.7.3", "open": "^8.4.2", "zod": "3.22.3", "remotion": "4.0.314", "@remotion/player": "4.0.314", "@remotion/media-parser": "4.0.314", "@remotion/studio-shared": "4.0.314", "@remotion/renderer": "4.0.314", "@remotion/media-utils": "4.0.314", "@remotion/webcodecs": "4.0.314", "@remotion/zod-types": "4.0.314"}, "devDependencies": {"react": "19.0.0", "react-dom": "19.0.0", "@types/semver": "^7.3.4", "eslint": "9.19.0", "@remotion/eslint-config-internal": "4.0.314"}, "publishConfig": {"access": "public"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "module": "./dist/esm/index.mjs", "import": "./dist/esm/index.mjs"}, "./renderEntry": {"types": "./dist/renderEntry.d.ts", "require": "./dist/renderEntry.js", "module": "./dist/esm/renderEntry.mjs", "import": "./dist/esm/renderEntry.mjs"}, "./internals": {"types": "./dist/internals.d.ts", "require": "./dist/internals.js", "module": "./dist/esm/internals.mjs", "import": "./dist/esm/internals.mjs"}, "./previewEntry": {"types": "./dist/previewEntry.d.ts", "require": "./dist/previewEntry.js", "module": "./dist/esm/previewEntry.mjs", "import": "./dist/esm/previewEntry.mjs"}}, "homepage": "https://www.remotion.dev/docs/studio/api", "typesVersions": {">=1.0": {"renderEntry": ["./dist/renderEntry.d.ts"], "internals": ["./dist/internals.d.ts"], "previewEntry": ["./dist/previewEntry.d.ts"]}}, "scripts": {"lint": "eslint src", "make": "tsc -d && bun --env-file=../.env.bundle bundle.ts", "test": "bun test src", "formatting": "prettier src --check"}}