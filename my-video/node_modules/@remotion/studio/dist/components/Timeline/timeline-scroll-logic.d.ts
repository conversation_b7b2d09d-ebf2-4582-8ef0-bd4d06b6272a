export declare const canScrollTimelineIntoDirection: () => {
    canScrollRight: boolean;
    canScrollLeft: boolean;
};
export declare const getFrameWhileScrollingLeft: ({ durationInFrames, width, }: {
    durationInFrames: number;
    width: number;
}) => number;
export declare const isCursorInViewport: ({ frame, durationInFrames, }: {
    frame: number;
    durationInFrames: number;
}) => boolean;
export declare const ensureFrameIsInViewport: ({ direction, durationInFrames, frame, }: {
    direction: "fit-left" | "fit-right" | "page-right" | "page-left" | "center";
    durationInFrames: number;
    frame: number;
}) => void;
export declare const scrollToTimelineXOffset: (scrollPos: number) => void;
export declare const getScrollPositionForCursorOnLeftEdge: ({ nextFrame, durationInFrames, }: {
    nextFrame: number;
    durationInFrames: number;
}) => number;
export declare const getScrollPositionForCursorOnRightEdge: ({ nextFrame, durationInFrames, }: {
    nextFrame: number;
    durationInFrames: number;
}) => number;
export declare const getFrameIncrementFromWidth: (durationInFrames: number, width: number) => number;
export declare const getFrameWhileScrollingRight: ({ durationInFrames, width, }: {
    durationInFrames: number;
    width: number;
}) => number;
export declare const getFrameFromX: ({ clientX, durationInFrames, width, extrapolate, }: {
    clientX: number;
    durationInFrames: number;
    width: number;
    extrapolate: "clamp" | "extend";
}) => number;
export declare const zoomAndPreserveCursor: ({ oldZoom, newZoom, currentFrame, currentDurationInFrames, }: {
    oldZoom: number;
    newZoom: number;
    currentFrame: number;
    currentDurationInFrames: number;
}) => void;
