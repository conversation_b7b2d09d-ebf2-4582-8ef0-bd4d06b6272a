import React from "react";
import {
  AbsoluteFill,
  interpolate,
  spring,
  useCurrentFrame,
  useVideoConfig,
} from "remotion";

export interface Video1Props {
  title: string;
  subtitle: string;
}

export const Video1: React.FC<Video1Props> = ({ title, subtitle }) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // Animation for title entrance
  const titleProgress = spring({
    frame,
    fps,
    config: {
      damping: 200,
    },
  });

  // Animation for subtitle entrance (delayed)
  const subtitleProgress = spring({
    frame: frame - 30, // Start 1 second later
    fps,
    config: {
      damping: 200,
    },
  });

  // Background color animation
  const backgroundColor = interpolate(
    frame,
    [0, durationInFrames / 2, durationInFrames],
    ["#667eea", "#764ba2", "#f093fb"]
  );

  // Title scale and opacity
  const titleScale = interpolate(titleProgress, [0, 1], [0.5, 1]);
  const titleOpacity = interpolate(titleProgress, [0, 1], [0, 1]);

  // Subtitle translate and opacity
  const subtitleTranslateY = interpolate(subtitleProgress, [0, 1], [50, 0]);
  const subtitleOpacity = interpolate(subtitleProgress, [0, 1], [0, 1]);

  return (
    <AbsoluteFill
      style={{
        background: `linear-gradient(45deg, ${backgroundColor}, #ffecd2)`,
        justifyContent: "center",
        alignItems: "center",
        fontFamily: "Arial, sans-serif",
      }}
    >
      {/* Title */}
      <div
        style={{
          fontSize: 120,
          fontWeight: "bold",
          color: "white",
          textAlign: "center",
          textShadow: "0 4px 8px rgba(0,0,0,0.3)",
          transform: `scale(${titleScale})`,
          opacity: titleOpacity,
          marginBottom: 40,
        }}
      >
        {title}
      </div>

      {/* Subtitle */}
      <div
        style={{
          fontSize: 60,
          color: "white",
          textAlign: "center",
          textShadow: "0 2px 4px rgba(0,0,0,0.3)",
          transform: `translateY(${subtitleTranslateY}px)`,
          opacity: subtitleOpacity,
          maxWidth: "80%",
        }}
      >
        {subtitle}
      </div>

      {/* Decorative elements */}
      <div
        style={{
          position: "absolute",
          top: "10%",
          left: "10%",
          width: 100,
          height: 100,
          borderRadius: "50%",
          background: "rgba(255,255,255,0.2)",
          transform: `rotate(${frame * 2}deg)`,
        }}
      />
      <div
        style={{
          position: "absolute",
          bottom: "15%",
          right: "15%",
          width: 150,
          height: 150,
          borderRadius: "50%",
          background: "rgba(255,255,255,0.1)",
          transform: `rotate(${-frame * 1.5}deg)`,
        }}
      />
    </AbsoluteFill>
  );
};
