-- Script AppleScript para cortar video con QuickTime Player
-- Del minuto 12 al 16

set videoPath to (POSIX path of (path to current application)) & "Contents/Resources/public/1.mp4"
set outputPath to (POSIX path of (path to current application)) & "Contents/Resources/output/video-cortado-min12-16.mov"

-- Convertir a rutas de macOS
set videoFile to POSIX file videoPath as alias
set outputFolder to (POSIX path of (path to current application)) & "Contents/Resources/output/"

tell application "QuickTime Player"
    activate
    
    -- Abrir el video
    open videoFile
    
    -- Obtener el documento actual
    set currentDoc to front document
    
    -- Configurar los tiempos (en segundos)
    set startTime to 720 -- 12 minutos
    set endTime to 960   -- 16 minutos
    
    -- Seleccionar el rango de tiempo
    tell currentDoc
        -- Ir al tiempo de inicio
        set current time to startTime
        
        -- Seleccionar desde el inicio hasta el final del corte
        select from startTime to endTime
        
        -- Recortar a la selección
        trim
        
        -- Exportar el video
        export in outputPath using settings preset "480p"
    end tell
    
    -- Cerrar el documento
    close currentDoc
    
end tell

display dialog "Video cortado exitosamente!" buttons {"OK"} default button "OK"
