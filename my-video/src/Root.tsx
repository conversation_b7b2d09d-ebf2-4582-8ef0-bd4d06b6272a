import "./index.css";
import { Composition, staticFile, CalculateMetadataFunction } from "remotion";
import {
  CaptionedVideo,
  calculateCaptionedVideoMetadata,
  captionedVideoSchema,
} from "./CaptionedVideo";
import { Video1 } from "./Video1";
import { getVideoMetadata } from "@remotion/media-utils";

// Calculate metadata for Video1
export const calculateVideo1Metadata: CalculateMetadataFunction<{}> = async () => {
  const fps = 30;
  const metadata = await getVideoMetadata(staticFile("1.mp4"));

  return {
    fps,
    durationInFrames: Math.floor(metadata.durationInSeconds * fps),
  };
};

// Each <Composition> is an entry in the sidebar!

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="CaptionedVideo"
        component={CaptionedVideo}
        calculateMetadata={calculateCaptionedVideoMetadata}
        schema={captionedVideoSchema}
        width={1080}
        height={1920}
        defaultProps={{
          src: staticFile("sample-video.mp4"),
        }}
      />
      <Composition
        id="Video1"
        component={Video1}
        calculateMetadata={calculateVideo1Metadata}
        width={1080}
        height={1920}
      />
    </>
  );
};
