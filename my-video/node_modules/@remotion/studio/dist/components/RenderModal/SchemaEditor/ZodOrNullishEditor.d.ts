import type { z } from 'zod';
import type { UpdaterFunction } from './ZodSwitch';
import type { JSONPath } from './zod-types';
export declare const ZodOrNullishEditor: React.FC<{
    showSaveButton: boolean;
    jsonPath: JSONPath;
    value: unknown;
    defaultValue: unknown;
    schema: z.ZodType<PERSON>ny;
    innerSchema: z.ZodType<PERSON>ny;
    setValue: UpdaterFunction<unknown>;
    onSave: UpdaterFunction<unknown>;
    onRemove: null | (() => void);
    nullishValue: null | undefined;
    saving: boolean;
    saveDisabledByParent: boolean;
    mayPad: boolean;
}>;
