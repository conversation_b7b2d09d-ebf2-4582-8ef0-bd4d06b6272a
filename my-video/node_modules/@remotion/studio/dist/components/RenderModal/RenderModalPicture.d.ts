import type { ColorSpace, PixelFormat, StillImageFormat, VideoImageFormat } from '@remotion/renderer';
import React from 'react';
import type { ComboboxValue } from '../NewComposition/ComboBox';
import type { SegmentedControlItem } from '../SegmentedControl';
import type { RenderType } from './RenderModalAdvanced';
declare const qualityControlModes: readonly ["crf", "bitrate"];
export type QualityControl = (typeof qualityControlModes)[number];
export declare const RenderModalPicture: React.FC<{
    readonly renderMode: RenderType;
    readonly scale: number;
    readonly setScale: React.Dispatch<React.SetStateAction<number>>;
    readonly pixelFormat: PixelFormat;
    readonly colorSpace: ColorSpace;
    readonly setColorSpace: React.Dispatch<React.SetStateAction<ColorSpace>>;
    readonly imageFormatOptions: SegmentedControlItem[];
    readonly setQualityControl: React.Dispatch<React.SetStateAction<QualityControl>>;
    readonly qualityControlType: QualityControl | null;
    readonly videoImageFormat: VideoImageFormat;
    readonly stillImageFormat: StillImageFormat;
    readonly setJpegQuality: React.Dispatch<React.SetStateAction<number>>;
    readonly jpegQuality: number;
    readonly maxCrf: number;
    readonly minCrf: number;
    readonly setCrf: React.Dispatch<React.SetStateAction<number>>;
    readonly setCustomTargetVideoBitrateValue: React.Dispatch<React.SetStateAction<string>>;
    readonly crf: number | null;
    readonly customTargetVideoBitrate: string;
    readonly shouldDisplayQualityControlPicker: boolean;
    readonly pixelFormatOptions: ComboboxValue[];
    readonly encodingBufferSize: string | null;
    readonly setEncodingBufferSize: React.Dispatch<React.SetStateAction<string | null>>;
    readonly encodingMaxRate: string | null;
    readonly setEncodingMaxRate: React.Dispatch<React.SetStateAction<string | null>>;
}>;
export {};
