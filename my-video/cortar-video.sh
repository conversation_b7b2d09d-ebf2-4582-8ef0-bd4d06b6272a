#!/bin/bash

# Script para cortar video del minuto 12 al 16 usando herramientas nativas de macOS

echo "🎬 Cortando video del minuto 12 al 16..."

# Verificar si el archivo de entrada existe
if [ ! -f "public/1.mp4" ]; then
    echo "❌ Error: No se encontró el archivo public/1.mp4"
    exit 1
fi

# Crear directorio de salida si no existe
mkdir -p output

# Usar avconvert (herramienta nativa de macOS) para cortar el video
# Tiempo de inicio: 12 minutos = 720 segundos
# Duración: 4 minutos = 240 segundos

echo "⏱️  Cortando desde el minuto 12 (720s) por 4 minutos (240s)..."

# Comando avconvert para cortar el video (sintaxis corregida)
avconvert --source "public/1.mp4" \
          --output "output/video-cortado-min12-16.mp4" \
          --preset "PresetHighestQuality" \
          --start 720 \
          --duration 240 \
          --replace \
          --progress

if [ $? -eq 0 ]; then
    echo "✅ Video cortado exitosamente!"
    echo "📁 Archivo guardado en: output/video-cortado-min12-16.mp4"
    
    # Mostrar información del archivo
    echo "📊 Información del archivo:"
    ls -lh "output/video-cortado-min12-16.mp4"
else
    echo "❌ Error al cortar el video"
    exit 1
fi
