import type { z } from 'zod';
import type { UpdaterFunction } from './ZodSwitch';
import type { JSONPath } from './zod-types';
export declare const ZodDiscriminatedUnionEditor: React.FC<{
    schema: z.ZodTypeAny;
    setValue: UpdaterFunction<Record<string, unknown>>;
    value: Record<string, unknown>;
    defaultValue: Record<string, unknown>;
    mayPad: boolean;
    jsonPath: JSONPath;
    onRemove: null | (() => void);
    onSave: UpdaterFunction<unknown>;
    showSaveButton: boolean;
    saving: boolean;
    saveDisabledByParent: boolean;
}>;
