#!/usr/bin/env python3
"""
Script para cortar video del minuto 12 al 16 usando moviepy
"""

import os
import sys

def install_moviepy():
    """Instala moviepy si no está disponible"""
    try:
        from moviepy.editor import VideoFileClip
        return True
    except ImportError:
        print("📦 Instalando moviepy...")
        os.system("pip3 install moviepy")
        try:
            from moviepy.editor import VideoFileClip
            return True
        except ImportError:
            print("❌ Error: No se pudo instalar moviepy")
            return False

def cortar_video():
    """Corta el video del minuto 12 al 16"""
    
    # Verificar e instalar moviepy
    if not install_moviepy():
        return False
    
    try:
        from moviepy.editor import VideoFileClip
        
        # Rutas de archivos
        input_file = "public/1.mp4"
        output_dir = "output"
        output_file = os.path.join(output_dir, "video-cortado-min12-16.mp4")
        
        # Verificar que el archivo de entrada existe
        if not os.path.exists(input_file):
            print(f"❌ Error: No se encontró el archivo {input_file}")
            return False
        
        # Crear directorio de salida
        os.makedirs(output_dir, exist_ok=True)
        
        print("🎬 Cortando video del minuto 12 al 16...")
        print(f"📁 Archivo de entrada: {input_file}")
        print(f"📁 Archivo de salida: {output_file}")
        
        # Cargar el video
        print("⏳ Cargando video...")
        video = VideoFileClip(input_file)
        
        # Obtener información del video
        duration = video.duration
        print(f"⏱️  Duración total del video: {duration:.2f} segundos ({duration/60:.2f} minutos)")
        
        # Definir tiempos de corte
        start_time = 12 * 60  # 12 minutos = 720 segundos
        end_time = 16 * 60    # 16 minutos = 960 segundos
        
        # Verificar que los tiempos están dentro del rango del video
        if start_time >= duration:
            print(f"❌ Error: El tiempo de inicio ({start_time}s) es mayor que la duración del video ({duration}s)")
            return False
        
        if end_time > duration:
            print(f"⚠️  Advertencia: El tiempo de fin ({end_time}s) es mayor que la duración del video ({duration}s)")
            end_time = duration
            print(f"🔧 Ajustando tiempo de fin a {end_time}s")
        
        print(f"✂️  Cortando desde {start_time}s hasta {end_time}s...")
        
        # Cortar el video
        video_cortado = video.subclip(start_time, end_time)
        
        # Guardar el video cortado
        print("💾 Guardando video cortado...")
        video_cortado.write_videofile(
            output_file,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # Limpiar memoria
        video.close()
        video_cortado.close()
        
        print("✅ Video cortado exitosamente!")
        print(f"📁 Archivo guardado en: {output_file}")
        
        # Mostrar información del archivo
        if os.path.exists(output_file):
            size = os.path.getsize(output_file) / (1024 * 1024)  # MB
            print(f"📊 Tamaño del archivo: {size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error al procesar el video: {str(e)}")
        return False

if __name__ == "__main__":
    success = cortar_video()
    sys.exit(0 if success else 1)
