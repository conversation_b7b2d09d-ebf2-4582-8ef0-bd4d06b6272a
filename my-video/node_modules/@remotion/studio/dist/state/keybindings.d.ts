import React from 'react';
export type KeyEventType = 'keydown' | 'keyup' | 'keypress';
type KeyListenerCallback = (e: KeyboardEvent) => void;
export type RegisteredKeybinding = {
    registeredFromPane: string;
    id: string;
    key: string;
    event: KeyEventType;
    callback: KeyListenerCallback;
};
type KeybindingContextType = {
    registerKeybinding: (binding: RegisteredKeybinding) => void;
    unregisterKeybinding: (binding: RegisteredKeybinding) => void;
    unregisterPane: (paneId: string) => void;
};
export declare const KeybindingContext: React.Context<KeybindingContextType>;
export declare const KeybindingContextProvider: React.FC<{
    readonly children: React.ReactNode;
}>;
export {};
