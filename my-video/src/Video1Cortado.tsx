import React from "react";
import {
  AbsoluteFill,
  OffthreadVideo,
  staticFile,
} from "remotion";

export const Video1Cortado: React.FC = () => {
  // Configuración del corte:
  // Inicio: minuto 12 = 12 * 60 = 720 segundos
  // Fin: minuto 16 = 16 * 60 = 960 segundos
  const startFrom = 12 * 60; // 720 segundos
  const endAt = 16 * 60; // 960 segundos

  return (
    <AbsoluteFill>
      <OffthreadVideo
        src={staticFile("1.mp4")}
        startFrom={startFrom}
        endAt={endAt}
        style={{
          width: "100%",
          height: "100%",
          objectFit: "cover",
        }}
      />
    </AbsoluteFill>
  );
};
